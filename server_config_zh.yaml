
# 服务参数
host: 0.0.0.0            # 服务地址
heartbeat_interval: 3000   # 服务心跳间隔（默认30秒）

# 数据收发和解析参数（需与客户端程序一致约定，pcm 数据流需要先符合以下标准化参数，再经过base64编码再传输）
valid_sample_rate_list: [44100, 16000, 8000]   # base64编码前的 pcm 数据流的采样率列表 [合法]
expected_sample_rate: 16000  # base64编码前的 pcm 数据流的采样率 [推荐]
expected_sample_width: 2     # base64编码前的 pcm 数据流的位宽 [合法]
expected_sample_channels: 1  # base64编码前的 pcm 数据流的通道数 [合法]
expected_data_size: 12800    # 客户端传输的 pcm 数据流最大字节数，非最后一个数据包的 pcm 数据流大小必须等于此值，最后一个数据包的 pcm 数据流大小必须小于此值 [合法]
expected_time_interval: 600    # 客户端传输的数据包最大时间间隔 [合法]

# 模型加载参数
onnx_dir: /ws/MODELS/online_onnx_zh   # 模型路径
quant: true   # 默认使用量化模型（适合并发超过4路的情况，达到加速）

# 仅中文支持：
context_list_path: "/ws/MODELS/online_onnx_zh/hotwords.txt"   # 定制热词文件路径，此参数仅中文支持
blank_interval: 0.5   # 若说话间隙超过此值（秒），则添加空格，此参数仅中文支持

# 分隔符配置
default_separator: "，"   # 中文默认分隔符（全角逗号）
