#! /user/bin/env python
#! -*- coding: utf-8 -*-

from infer_onnx_lid import predict_audio, load_model, load_dict, VALID_EXT
import argparse, os, json
import yaml
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

def get_args():
    parser = argparse.ArgumentParser(description=f'Detect language for audio, support extention: {VALID_EXT}')
    parser.add_argument('--input', default="/ws/test_data/test.list", help=f'Single audio file with extention in {VALID_EXT}, or a list file containing multiple audio files (eg. /ws/test_data/test.list)')
    parser.add_argument('--output', default="/ws/test_data/test.list.output", help='Save path when `--input` is a list file')
    parser.add_argument('--onnx_path', default="/ws/res/lid.onnx", help='Path of onnx model')
    parser.add_argument('--config', default="/ws/res/config.yaml", help='Path of config.yaml')
    parser.add_argument('--spk2id', default="/ws/res/spk2id.json", help="Path of spk2id.json")
    parser.add_argument('--do_cmvn', required=False, default=True)
    parser.add_argument('--verbose','-v',action='store_true', help='Enable verbose mode for more detailed print information')
    parser.add_argument('--num_threads', type=int, default=8)
    args = parser.parse_args()
    return args

def load(args):
    print(f"---------------")
    with open(args.config, 'r') as fin:
        configs = yaml.load(fin, Loader=yaml.FullLoader)
    session, input_names, output_names = load_model(args)
    spk2id_dict = load_dict(args)
    print(f"---------------")
    return session, input_names, output_names, configs, spk2id_dict, args.do_cmvn

def infer(wav_path, params):
    inp = {'wav': wav_path, 'spk': ''}
    return predict_audio(
        inp,
        *params
    )

if __name__ == '__main__':
    # 处理参数
    args = get_args()
    
    # 加载配置、模型、字典
    params = load(args)
    
    # 处理输入
    wav_path = args.input
    ext = os.path.splitext(wav_path)[1]

    # 单个输入
    if ext in VALID_EXT:
        result = infer(wav_path, params)
        print(result)
    # 多个输入
    elif ext in ['.txt', '.list']:
        assert os.path.exists(wav_path), f"Error: File {wav_path} does not exists."
        if args.output:
            fout = open(args.output, 'w', encoding='utf-8')
        data = []
        with open(wav_path, 'r', encoding='utf-8')as fr:
            for line in fr.readlines():
                dic = json.loads(line.strip())
                data.append(dic)
        # 多线程执行
        workers = min(len(data), args.num_threads)
        with ThreadPoolExecutor(max_workers=workers) as executor:
            futures = [
                executor.submit(predict_audio, 
                                dic, 
                                *params
                                )
                for dic in data
            ]
            for future in as_completed(futures):
                try:
                    result = future.result()
                    fout.write(json.dumps(result, ensure_ascii=False) +'\n')
                    if args.verbose:
                        result = json.dumps(result, indent=4, ensure_ascii=False)
                        print(result)
                    else:
                        print(f"Detect language: {result['predict']} | {result['wav']}")
                    

                except Exception as e:
                    print(f"Error processing line: {e}")
                    traceback.print_exc()
                    exit(1)

            fout.close()
    else:
        print(f"Valid extention for single audio: {VALID_EXT}, multiple audio please refer to: /ws/test_data/test.list")
        exit(1)
