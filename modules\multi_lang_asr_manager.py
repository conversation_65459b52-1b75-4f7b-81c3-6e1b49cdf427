#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多语种ASR模型管理器
负责管理多个语种的ASR模型，支持动态加载和切换
"""

import os
import yaml
from typing import Dict, Optional, Any
from modules.logger import logger
from modules.decoder import load_onnx, ONNX_SESSIONS
from modules.symbol_table import SymbolTable
from modules.config import ConfigManager


class MultiLangASRManager:
    """多语种ASR模型管理器"""
    
    def __init__(self, base_args):
        """
        初始化多语种ASR管理器
        
        Args:
            base_args: 基础配置参数
        """
        self.base_args = base_args
        self.supported_languages = ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
        self.loaded_models = {}  # 存储已加载的模型信息
        self.symbol_tables = {}  # 存储各语种的词表
        self.configs = {}  # 存储各语种的配置
        
        logger.info("初始化多语种ASR管理器")
        
    def get_model_path(self, lang_code: str) -> str:
        """获取指定语种的模型路径"""
        base_model_dir = os.path.dirname(self.base_args.onnx_dir)
        return os.path.join(base_model_dir, f"online_onnx_{lang_code}")
    
    def load_language_model(self, lang_code: str) -> bool:
        """
        加载指定语种的ASR模型
        
        Args:
            lang_code: 语种代码
            
        Returns:
            bool: 是否加载成功
        """
        if lang_code in self.loaded_models:
            logger.info(f"语种 {lang_code} 的模型已加载")
            return True
            
        try:
            # 构建语种特定的参数
            lang_args = self._build_lang_args(lang_code)
            if not lang_args:
                return False
            
            # 保存当前全局ONNX_SESSIONS
            original_sessions = ONNX_SESSIONS.copy()
            ONNX_SESSIONS.clear()
            
            # 加载语种特定的模型
            metadatas = load_onnx(lang_args)
            
            # 保存加载的模型会话
            self.loaded_models[lang_code] = {
                'sessions': ONNX_SESSIONS.copy(),
                'metadatas': metadatas,
                'args': lang_args
            }
            
            # 创建配置管理器
            config_manager = ConfigManager(lang_args, metadatas)
            self.configs[lang_code] = config_manager.configs
            
            # 创建词表
            self.symbol_tables[lang_code] = SymbolTable(
                lang_args.onnx_dir, 
                lang_args.dict_path, 
                lang_code
            )
            
            # 恢复原始ONNX_SESSIONS
            ONNX_SESSIONS.clear()
            ONNX_SESSIONS.update(original_sessions)
            
            logger.info(f"成功加载语种 {lang_code} 的ASR模型")
            return True
            
        except Exception as e:
            logger.error(f"加载语种 {lang_code} 的ASR模型失败: {e}")
            return False
    
    def _build_lang_args(self, lang_code: str):
        """构建语种特定的参数"""
        try:
            # 获取模型路径
            model_path = self.get_model_path(lang_code)
            if not os.path.exists(model_path):
                logger.warning(f"语种 {lang_code} 的模型路径不存在: {model_path}")
                return None
            
            # 复制基础参数
            import copy
            lang_args = copy.deepcopy(self.base_args)
            
            # 设置语种特定的路径
            lang_args.onnx_dir = model_path
            lang_args.dict_path = os.path.join(model_path, 'units.txt')
            lang_args.onnx_config = os.path.join(model_path, 'train.yaml')
            lang_args.context_list_path = os.path.join(model_path, 'hotwords.txt')
            lang_args.lang_code = lang_code
            
            # 检查必要文件是否存在
            if not os.path.exists(lang_args.dict_path):
                logger.warning(f"语种 {lang_code} 的词表文件不存在: {lang_args.dict_path}")
                return None
                
            if not os.path.exists(lang_args.onnx_config):
                logger.warning(f"语种 {lang_code} 的配置文件不存在: {lang_args.onnx_config}")
                return None
            
            return lang_args
            
        except Exception as e:
            logger.error(f"构建语种 {lang_code} 参数失败: {e}")
            return None
    
    def switch_to_language(self, lang_code: str) -> bool:
        """
        切换到指定语种的模型
        
        Args:
            lang_code: 目标语种代码
            
        Returns:
            bool: 是否切换成功
        """
        if lang_code not in self.supported_languages:
            logger.warning(f"不支持的语种: {lang_code}")
            return False
        
        # 如果模型未加载，先加载
        if lang_code not in self.loaded_models:
            if not self.load_language_model(lang_code):
                return False
        
        try:
            # 切换全局ONNX_SESSIONS到目标语种
            ONNX_SESSIONS.clear()
            ONNX_SESSIONS.update(self.loaded_models[lang_code]['sessions'])
            
            logger.info(f"成功切换到语种: {lang_code}")
            return True
            
        except Exception as e:
            logger.error(f"切换到语种 {lang_code} 失败: {e}")
            return False
    
    def get_symbol_table(self, lang_code: str) -> Optional[SymbolTable]:
        """获取指定语种的词表"""
        return self.symbol_tables.get(lang_code)
    
    def get_config(self, lang_code: str) -> Optional[Dict]:
        """获取指定语种的配置"""
        return self.configs.get(lang_code)
    
    def get_args(self, lang_code: str) -> Optional[Any]:
        """获取指定语种的参数"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['args']
        return None
    
    def preload_common_languages(self, languages: list = None):
        """
        预加载常用语种的模型
        
        Args:
            languages: 要预加载的语种列表，默认为['zh', 'en']
        """
        if languages is None:
            languages = ['zh', 'en']
        
        logger.info(f"开始预加载语种: {languages}")
        
        for lang_code in languages:
            if lang_code in self.supported_languages:
                self.load_language_model(lang_code)
            else:
                logger.warning(f"跳过不支持的语种: {lang_code}")
        
        logger.info("预加载完成")
    
    def is_language_loaded(self, lang_code: str) -> bool:
        """检查指定语种是否已加载"""
        return lang_code in self.loaded_models
    
    def get_loaded_languages(self) -> list:
        """获取已加载的语种列表"""
        return list(self.loaded_models.keys())
    
    def unload_language(self, lang_code: str):
        """卸载指定语种的模型"""
        if lang_code in self.loaded_models:
            del self.loaded_models[lang_code]
            if lang_code in self.symbol_tables:
                del self.symbol_tables[lang_code]
            if lang_code in self.configs:
                del self.configs[lang_code]
            logger.info(f"已卸载语种 {lang_code} 的模型")
    
    def cleanup(self):
        """清理所有资源"""
        logger.info("清理多语种ASR管理器资源")
        self.loaded_models.clear()
        self.symbol_tables.clear()
        self.configs.clear()
        ONNX_SESSIONS.clear()
