服务说明: 
1. 功能: 音频实时识别的 websocket 服务
2. 语种: 目前支持中文(zh)、英文(en)、俄语(ru)、维语(ug)、哈萨克语(kkin)
3. 步骤: 
    3.1 配置文件: 以英语为例, 配置文件"server_config_en.yaml"中给出了必要参数键值:
        "服务参数":
            host: 0.0.0.0            # 服务地址
            heartbeat_interval: 30   # 服务心跳间隔（默认30秒）

        "模型加载参数":
            onnx_dir: /ws/MODELS/online_onnx_en   # 模型路径
            quant: true   # 是否使用量化模型, 默认开启, 适合并发超过4路的情况, 达到加速效果

        "仅中文支持":
            context_list_path: /ws/MODELS/online_onnx_zh/hotwords.txt   # 热词文件路径, 增大热词的输出概率
            blank_interval: 0.5   # 若说话间隙超过此值（秒）, 则添加空格

        "数据收发和解析参数": 
            (此部分参数仅供开发人员包装ws客户端请求时参考, 请勿改动, 
            客户端请求时, 需要符合服务端的数据传输规范, 
            即pcm 数据流需要先符合以下标准化参数, 再经过base64编码再传输, 
            详情请参考客户端示例脚本 'client.py')
            valid_sample_rate_list: [44100, 16000, 8000] # base64编码前的 pcm 数据流的合法采样率列表
            expected_sample_rate: 16000  # base64编码前的 pcm 数据流的采样率,  推荐使用 16000,  如果采用其余合法采样率, 请相应更改此值
            expected_sample_width: 2     # base64编码前的 pcm 数据流的合法位宽
            expected_sample_channels: 1  # base64编码前的 pcm 数据流的合法通道数
            expected_data_size: 12800    # 客户端传输的 pcm 数据流最大字节数, 非最后一个数据包的 pcm 数据流大小必须等于此值, 最后一个数据包的 pcm 数据流大小必须小于此值
            expected_time_interval: 6    # 客户端传输的数据包最大时间间隔


    3.2 启动服务:
        3.2.1 单语种模式: python <服务脚本> <语种代码> <服务端口>, 例如 "python server.py en 12345"
        3.2.2 多语种模式: python <服务脚本> multi <服务端口>, 例如 "python server.py multi 12345"
              多语种模式支持自动语种识别(LID)，根据音频内容自动选择合适的ASR模型
              需要在配置文件中设置lid_model_path参数指向LID模型路径

    3.3 客户请求:
        3.3.1 单语种模式: python <客户端脚本> <语种代码> <服务端口>, 例如 "python client.py en 12345"
        3.3.2 多语种模式: python <客户端脚本> multi <服务端口>, 例如 "python client.py multi 12345"
