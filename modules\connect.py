#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  connect.py
# Time    :  2025/04/17 14:20:49
# Author  :  lh
# Version :  1.0
# Description:  


from typing import Dict, Any, Optional, Tuple, List
import asyncio
import torch
import uuid, base64, time
from datetime import datetime
from fastapi import WebSocket
# from memory_profiler import profile

from modules.logger import logger
from modules.feature import FeaturePipeline
from modules.decoder import ASRDecoder


def format_time(timestamp):
    return datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]

class ConnectionManager:
    """
    连接管理类, 用于处理客户端连接、音频数据解析和管理解码。
    
    Attributes:
        active_connection (Dict[str, Any]): 存储活动连接的字典
        client_states (Dict[str, Dict[str, Any]]): 客户端状态信息
        configs (dict): 包含配置参数的字典
        feat_pipe (Any): 特征提取管道
        valid_sample_rate_list (List[int]): 支持的采样率列表
        expected_data_size (int): 预期音频数据大小
        packet_interval (float): 数据包间隔时间(秒)
        required_decoding_window (int): 解码所需的最小帧数
    """

    def __init__(self, args, configs, feat_pipe, symbol_table):
        """每个ws连接维护独立数据和缓存队列"""
        # ws连接
        self.active_connection: Dict[str: WebSocket] = {}
        # 客户端状态
        self.client_states: Dict[str: dict] = {}

        self.args = args
        self.configs = configs
        self.packet_interval = args.expected_time_interval
        self.expected_data_size = args.expected_data_size
        self.valid_sample_rate_list = args.valid_sample_rate_list
        self.feat_pipe = feat_pipe
        self.symbol_table = symbol_table
        # 一帧的时间长度
        self.frame_samples = feat_pipe.frame_length / 1000
        
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        # 存储ws连接
        self.active_connection[client_id] = websocket
        # 初始化客户状态
        self.client_states[client_id] = {
            "feat_buffer": [],   # 缓存的特征
            "frame_nums": 0,     # 累计的帧数量
            "packet_index": -1,   # 到达数据包的索引
            "sample_rate": None,
            "last_packet_time": None,     # 上一个数据包到达的时间 (用于检查实时率)
            "is_first": True,   # 是否为第一个数据包  (接受到第一个数据包时置为False, 用于后续验证数据包大小、采样率)
            "is_final": False,  # 是否为最后一个数据包
            "is_final_result": False,  # 是否为最后的识别结果
            "custom_separator": None,  # 用户自定义分隔符，将在第一个数据包中设置
            "decoder": None,    # 解码器将在收到第一个数据包时初始化
            "decoder_result" : "",
            "message_id_base": f"client_id:{client_id}_{uuid.uuid4()}",
            "message_counter": 0
        }
    
    # @profile
    async def disconnect(self, client_id: str):
        try:
            logger.info(f"关闭 ws 连接")
            await self.active_connection[client_id].close()
        except:
            logger.info(f"ws 连接已经关闭了")
        # 移除ws连接
        if client_id in self.active_connection:
            del self.active_connection[client_id]
        # 移除客户状态, 并清空相关资源
        if client_id in self.client_states:
            # 清空缓存
            self.client_states[client_id]["feat_buffer"].clear()
            # 清除ASRDecoder实例
            if "decoder" in self.client_states[client_id]:
                del self.client_states[client_id]["decoder"]
            # 移除客户状态
            del self.client_states[client_id]

    def generate_message_id(self, client_id: str) -> str:
        self.client_states[client_id]["message_counter"] += 1
        base = self.client_states[client_id]["message_id_base"]
        index = self.client_states[client_id]["message_counter"]
        return f"{base}_{index}"

    async def on_error(self, code: int, message: str, client_id: str):
        """
        处理错误并发送错误信息给客户端
        Args:
            code (int): 错误码
            message (str): 错误描述
            client_id (str): 客户端唯一标识
        Returns:
            None
        """
        if self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            error_response = {
                    "code": code,
                    "state": "error",
                    "index": index,
                    "result": message,
                    "voice_id": client_id,
                    "message_id": self.generate_message_id(client_id),
                    "final": 1
            }
            logger.warning(f"client_id:{client_id} - <<< [发送] 错误信息: {error_response}")
            await self.active_connection[client_id].send_json(error_response)
            logger.info(f"client_id: {client_id} - 关闭连接，清理资源")
            await self.disconnect(client_id)

    async def on_result(self, result: str, client_id: str):
        """
        发送识别结果给客户端。
        Args:
            client_id (str): 客户端唯一标识
            result (str): 当前识别结果
        Returns:
            None
        """
        if self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            final = self.client_states[client_id]['is_final_result']
            response = {
                    "code": 200,
                    "state": "success",
                    "index": index,
                    "result": result,
                    "voice_id": client_id,
                    "message_id": self.generate_message_id(client_id),
                    "final": 1 if final else 0
            }
            logger.info(f"client_id:{client_id} - <<< [发送] 第{index}个数据包, 更新识别结果: \"{result}\"")
            await self.active_connection[client_id].send_json(response)

    async def on_check(self, client_id: str, json_data: Dict[str, Any]) -> bool:
        """
        检查数据包合法性, 解析音频数据并获取音频特征。
        Args:
            client_id (str): 客户端唯一标识
            json_data (Dict[str, Any]): 接收到的 JSON 数据
        Returns:
            bool: 检查结果(True 表示合法, False 表示非法)
        """
        if client_id not in self.active_connection:
            logger.error(f"客户已断开连接 \"{client_id}\"")
            raise KeyError(f"client disconnect: {client_id}")

        # 1. 检查必要字段
        # "index", 发送数据包的序号, 从0开始
        # "audio_data", Base64编码的音频数据, 数据长度需要满足固定要求
        # "sample_rate", 推荐16000
        # "is_final", True 代表最后一个数据包
        # "custom_separator", 可选的自定义分隔符参数
        required_keys = ["index", "audio_data", "sample_rate", "is_final"]
        for key in required_keys:
            if key not in json_data:
                await self.on_error(code=4001, message=f"Missing key: {key}, required keys: {required_keys}", client_id=client_id)
                return False

        # 处理可选的自定义分隔符参数（仅在第一个数据包中处理）
        if json_data["index"] == 0 and "custom_separator" in json_data:
            self.client_states[client_id]["custom_separator"] = json_data["custom_separator"]
            logger.info(f"client_id:{client_id} - 设置自定义分隔符: \"{json_data['custom_separator']}\"")

        # 如果解码器尚未初始化（第一个数据包），则初始化解码器
        if self.client_states[client_id]["decoder"] is None:
            custom_separator = self.client_states[client_id]["custom_separator"]
            self.client_states[client_id]["decoder"] = ASRDecoder(
                self.args, self.configs, self.symbol_table, custom_separator
            )
            self.required_decoding_window = self.client_states[client_id]["decoder"].decoding_window
            logger.info(f"client_id:{client_id} - 初始化解码器，分隔符: \"{self.client_states[client_id]['decoder'].blank}\"")


        # 2. 检查数据包索引, index 从0开始, 保证顺序到达
        index = json_data["index"]
        expected_index = self.client_states[client_id]['packet_index'] + 1
        if index != expected_index:
            await self.on_error(code=4002, message=f"Packet index error. Expected: {expected_index}, Got: {index}", client_id=client_id)
            return False
        # 更新索引状态
        self.client_states[client_id]['packet_index'] = index

        # 3. 是否为最后一个数据包
        is_final = json_data['is_final']
        if is_final:
            self.client_states[client_id]['is_final'] = True

        # 4. 检查数据包发送的实时率
        current_time = time.time()
        last_packet_time = self.client_states[client_id].get('last_packet_time', None)
        if last_packet_time is None:   # 第一个数据包
            self.client_states[client_id]['is_first'] = True
        else:
            self.client_states[client_id]['is_first'] = False
            if current_time - last_packet_time > self.packet_interval:
                current_time_fmt = format_time(current_time)
                last_time_fmt = format_time(last_packet_time)
                await self.on_error(
                    code=4003,
                    message=f"Audio packet interval timeout ({self.packet_interval} s). Last packet time: {last_time_fmt}, Current packet time: {current_time_fmt}",
                    client_id=client_id
                )
                return False
        # 更新数据包到达时间
        self.client_states[client_id]['last_packet_time'] = current_time  

        # 5. 检查采样率一致性
        sample_rate = json_data['sample_rate']
        last_sample_rate = self.client_states[client_id].get('sample_rate', None)
        
        if last_sample_rate is not None and sample_rate != last_sample_rate:
            await self.on_error(
                code=4004,
                message=f"Sample rate mismatch. Last: {last_sample_rate}, Current: {sample_rate}",
                client_id=client_id
            )
            return False

        if sample_rate not in self.valid_sample_rate_list:
            await self.on_error(
                code=4005,
                message=f"Invalid sample rate. Supported rates: {self.valid_sample_rate_list}",
                client_id=client_id
            )
            return False
    
        # 6.1 解码Base64编码的音频数据
        try:
            audio_data_base64 = json_data["audio_data"]
            pcm_bytes = base64.b64decode(audio_data_base64)
        except Exception as e:
            await self.on_error(code=4006, message=f"Audio data decoding error: {str(e)}. Value of \"audio_data\" shoud be encoded by base64, for example: packet = pcm_data[:{self.expected_data_size}], request_data[\"audio_data\"] = base64.b64encode(packet).decode('utf-8')", client_id=client_id)
            return False
        # 6.2 检查pcm音频数据流大小
        if is_final:
            if len(pcm_bytes) > self.expected_data_size:
                await self.on_error(
                    code=4007,
                    message=f"Final packet audio data size exceeds expected size (Expected: {self.expected_data_size}, Got: {len(pcm_bytes)})",
                    client_id=client_id
                )
                return False
        else:
            if len(pcm_bytes) != self.expected_data_size:
                await self.on_error(
                    code=4008,
                    message=f"Non-final packet audio data size mismatch (Expected: {self.expected_data_size}, Got: {len(pcm_bytes)}). Expected data size: based on 16000 sr, mono channel, 16-bit audio stream, 400 ms of audio data packet is sent each time",
                    client_id=client_id
                )
                return False

        # 6.3 提取特征并缓存, 不需要缓存原始 pcm 或 waveform
        try:
            waveform = self.feat_pipe.to_waveform(pcm_bytes)  # torch.Tensor
            if is_final and waveform.size(0) < self.frame_samples * sample_rate:
                # 最后一个数据包达不到最小数据要求（小于一个帧长）则跳过
                return True
            feat = self.feat_pipe.feat_func(waveform, sample_rate)   # torch.Tensor
            self.client_states[client_id]['feat_buffer'].append(feat)
            # logger.debug(f"client_id:{client_id} - >>> waveform shape: {waveform.shape}")
            # logger.debug(f"client_id:{client_id} - >>> feat shape: {feat.shape}")

            # 6.4 更新帧数
            cur_frames = feat.shape[0]
            self.client_states[client_id]['frame_nums'] += cur_frames
            logger.info(f"client_id:{client_id} - >>> [解析] 第{index}个数据包, 累计帧数: {self.client_states[client_id]['frame_nums']}")
            return True
        except Exception as e:
            await self.on_error(code=4009, message=f"Feature extraction error: {str(e)}", client_id=client_id)
            return False

    async def on_decode(self, client_id: str) -> Tuple[bool, Optional[str]]:
        """
        当帧数满足解码所需的最小帧数时, 开始逐 chunk 解码。
        Args:
            client_id (str): 客户端唯一标识
        Returns:
            Tuple[bool, Optional[str]]: 是否成功解码(True/False), 解码结果或空字符串
        """
        try:
            while True:
                frame_nums = self.client_states[client_id]['frame_nums']
                
                if frame_nums >= self.required_decoding_window:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 开始解码")
                    
                    # 拼接特征并添加 batch 维度
                    accum_feats = torch.cat(self.client_states[client_id]['feat_buffer'], dim=0).unsqueeze(0)
                    
                    is_final = self.client_states[client_id]['is_final']
                    asr_model = self.client_states[client_id]['decoder']
                    asr_model.decode(accum_feats, client_id, is_final) # 累积的解码结果存储在 asr_model.result 中
                    last_result = self.client_states[client_id]['decoder_result']
                    cur_result = asr_model.result
                    if is_final:
                        logger.info(f"client_id:{client_id} - *** 最后一个数据包完成解码 ***")
                        self.client_states[client_id]['is_final_result'] = True
                        return (True, cur_result)    # 最后一个数据包，无论识别结果变不变，都要发送响应数据包

                    if cur_result != last_result:
                        logger.debug(f"client_id:{client_id} - chunk 完成解码, 更新识别结果")
                        self.client_states[client_id]['decoder_result'] = cur_result
                        return (True, cur_result)
                    else:
                        logger.debug(f"client_id:{client_id} - chunk 完成解码, 识别结果不变")
                        return (True, "")
                else:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 继续等待数据包")
                    await asyncio.sleep(0.01)   # 低频轮询避免忙等待 (10ms 间隔)
                    return (True, "")
        
        except Exception as e:
            await self.on_error(code=5001, message=f"Decode error", client_id=client_id)
            return (False, None)
