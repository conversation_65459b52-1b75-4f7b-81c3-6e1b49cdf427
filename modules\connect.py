#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  connect.py
# Time    :  2025/04/17 14:20:49
# Author  :  lh
# Version :  1.0
# Description:  


from typing import Dict, Any, Optional, Tuple, List
import asyncio
import torch
import uuid, base64, time
from datetime import datetime
from fastapi import WebSocket
# from memory_profiler import profile

from modules.logger import logger
from modules.feature import FeaturePipeline
from modules.decoder import ASRDecoder
from modules.lid_manager import LIDManager


def format_time(timestamp):
    return datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]

class ConnectionManager:
    """
    连接管理类, 用于处理客户端连接、音频数据解析和管理解码。
    
    Attributes:
        active_connection (Dict[str, Any]): 存储活动连接的字典
        client_states (Dict[str, Dict[str, Any]]): 客户端状态信息
        configs (dict): 包含配置参数的字典
        feat_pipe (Any): 特征提取管道
        valid_sample_rate_list (List[int]): 支持的采样率列表
        expected_data_size (int): 预期音频数据大小
        packet_interval (float): 数据包间隔时间(秒)
        required_decoding_window (int): 解码所需的最小帧数
    """

    def __init__(self, args, configs, feat_pipe, symbol_table, lid_manager=None, multi_lang_asr_manager=None):
        """每个ws连接维护独立数据和缓存队列"""
        # ws连接
        self.active_connection: Dict[str: WebSocket] = {}
        # 客户端状态
        self.client_states: Dict[str: dict] = {}

        self.args = args
        self.configs = configs
        self.packet_interval = args.expected_time_interval
        self.expected_data_size = args.expected_data_size
        self.valid_sample_rate_list = args.valid_sample_rate_list
        self.feat_pipe = feat_pipe
        self.symbol_table = symbol_table
        self.lid_manager = lid_manager
        self.multi_lang_asr_manager = multi_lang_asr_manager
        # 一帧的时间长度
        self.frame_samples = feat_pipe.frame_length / 1000
        
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        # 存储ws连接
        self.active_connection[client_id] = websocket
        # 初始化客户状态
        self.client_states[client_id] = {
            "feat_buffer": [],   # 缓存的特征
            "frame_nums": 0,     # 累计的帧数量
            "packet_index": -1,   # 到达数据包的索引
            "sample_rate": None,
            "last_packet_time": None,     # 上一个数据包到达的时间 (用于检查实时率)
            "is_first": True,   # 是否为第一个数据包  (接受到第一个数据包时置为False, 用于后续验证数据包大小、采样率)
            "is_final": False,  # 是否为最后一个数据包
            "is_final_result": False,  # 是否为最后的识别结果
            "custom_separator": None,  # 用户自定义分隔符，将在第一个数据包中设置
            "decoder": None,    # 解码器将在收到第一个数据包时初始化
            "decoder_result" : "",
            "message_id_base": f"client_id:{client_id}_{uuid.uuid4()}",
            "message_counter": 0,
            # LID相关状态
            "lid_enabled": True,  # 是否启用语种识别
            "detected_language": None,  # 检测到的语种
            "language_confidence": 0.0,  # 语种识别置信度
            "audio_buffer": [],  # 用于LID的原始音频缓存
            "audio_duration": 0.0,  # 累计音频时长（秒）
            "lid_attempts": 0,  # LID尝试次数
            "lid_max_attempts": 6,  # 最大LID尝试次数（对应2.4秒）
            "lid_confirmed": False,  # LID结果是否已确认
            "vad_speech_detected": False,  # 是否检测到有效语音
            # 多语种对话支持
            "silence_frames": 0,  # 连续静音帧数
            "silence_threshold": 10,  # 静音阈值（帧数），超过此值认为是静音片段
            "last_speech_frame": -1,  # 最后一次检测到语音的帧索引
            "segment_count": 0,  # 语音片段计数
            # 多语种相关状态
            "current_language": None,  # 当前使用的语种
            "current_symbol_table": None,  # 当前使用的词表
            "current_config": None,  # 当前使用的配置
            "current_args": None,  # 当前使用的参数
        }
    
    # @profile
    async def disconnect(self, client_id: str):
        try:
            logger.info(f"关闭 ws 连接")
            await self.active_connection[client_id].close()
        except:
            logger.info(f"ws 连接已经关闭了")
        # 移除ws连接
        if client_id in self.active_connection:
            del self.active_connection[client_id]
        # 移除客户状态, 并清空相关资源
        if client_id in self.client_states:
            # 清空缓存
            self.client_states[client_id]["feat_buffer"].clear()
            # 清空LID音频缓存
            if "audio_buffer" in self.client_states[client_id]:
                self.client_states[client_id]["audio_buffer"].clear()
            # 清除ASRDecoder实例
            if "decoder" in self.client_states[client_id]:
                del self.client_states[client_id]["decoder"]
            # 移除客户状态
            del self.client_states[client_id]

    def generate_message_id(self, client_id: str) -> str:
        self.client_states[client_id]["message_counter"] += 1
        base = self.client_states[client_id]["message_id_base"]
        index = self.client_states[client_id]["message_counter"]
        return f"{base}_{index}"

    async def on_error(self, code: int, message: str, client_id: str):
        """
        处理错误并发送错误信息给客户端
        Args:
            code (int): 错误码
            message (str): 错误描述
            client_id (str): 客户端唯一标识
        Returns:
            None
        """
        if self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            error_response = {
                    "code": code,
                    "state": "error",
                    "index": index,
                    "result": message,
                    "voice_id": client_id,
                    "message_id": self.generate_message_id(client_id),
                    "final": 1
            }
            logger.warning(f"client_id:{client_id} - <<< [发送] 错误信息: {error_response}")
            await self.active_connection[client_id].send_json(error_response)
            logger.info(f"client_id: {client_id} - 关闭连接，清理资源")
            await self.disconnect(client_id)

    async def on_result(self, result: str, client_id: str):
        """
        发送识别结果给客户端。
        Args:
            client_id (str): 客户端唯一标识
            result (str): 当前识别结果
        Returns:
            None
        """
        if self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            final = self.client_states[client_id]['is_final_result']
            response = {
                    "code": 200,
                    "state": "success",
                    "index": index,
                    "result": result,
                    "voice_id": client_id,
                    "message_id": self.generate_message_id(client_id),
                    "final": 1 if final else 0
            }

            # 添加语种识别结果（如果可用且已确认）
            if (self.client_states[client_id].get("lid_confirmed", False) and
                self.client_states[client_id].get("detected_language")):
                response["language"] = self.client_states[client_id]["detected_language"]
                response["language_confidence"] = self.client_states[client_id].get("language_confidence", 0.0)

            logger.info(f"client_id:{client_id} - <<< [发送] 第{index}个数据包, 更新识别结果: \"{result}\"")
            await self.active_connection[client_id].send_json(response)

    async def on_check(self, client_id: str, json_data: Dict[str, Any]) -> bool:
        """
        检查数据包合法性, 解析音频数据并获取音频特征。
        Args:
            client_id (str): 客户端唯一标识
            json_data (Dict[str, Any]): 接收到的 JSON 数据
        Returns:
            bool: 检查结果(True 表示合法, False 表示非法)
        """
        if client_id not in self.active_connection:
            logger.error(f"客户已断开连接 \"{client_id}\"")
            raise KeyError(f"client disconnect: {client_id}")

        # 1. 检查必要字段
        # "index", 发送数据包的序号, 从0开始
        # "audio_data", Base64编码的音频数据, 数据长度需要满足固定要求
        # "sample_rate", 推荐16000
        # "is_final", True 代表最后一个数据包
        # "custom_separator", 可选的自定义分隔符参数
        required_keys = ["index", "audio_data", "sample_rate", "is_final"]
        for key in required_keys:
            if key not in json_data:
                await self.on_error(code=4001, message=f"Missing key: {key}, required keys: {required_keys}", client_id=client_id)
                return False

        # 处理可选参数（仅在第一个数据包中处理）
        if json_data["index"] == 0:
            # 处理自定义分隔符参数
            if "custom_separator" in json_data:
                self.client_states[client_id]["custom_separator"] = json_data["custom_separator"]
                logger.info(f"client_id:{client_id} - 设置自定义分隔符: \"{json_data['custom_separator']}\"")

            # 处理LID启用选项（可选，默认启用）
            if "enable_lid" in json_data:
                self.client_states[client_id]["lid_enabled"] = bool(json_data["enable_lid"])
                logger.info(f"client_id:{client_id} - LID功能: {'启用' if self.client_states[client_id]['lid_enabled'] else '禁用'}")

        # 延迟初始化解码器，等待LID结果确定语种后再初始化
        # 如果LID功能未启用或不可用，则立即初始化解码器
        should_init_decoder = (
            self.client_states[client_id]["decoder"] is None and
            (not self.client_states[client_id]["lid_enabled"] or
             not self.lid_manager or
             not self.lid_manager.is_available() or
             self.client_states[client_id]["lid_confirmed"])
        )

        if should_init_decoder:
            custom_separator = self.client_states[client_id]["custom_separator"]

            # 多语种模式下使用动态切换的配置
            if self.multi_lang_asr_manager and self.client_states[client_id]["current_args"]:
                # 使用切换后的语种配置
                decoder_args = self.client_states[client_id]["current_args"]
                decoder_configs = self.client_states[client_id]["current_config"]
                decoder_symbol_table = self.client_states[client_id]["current_symbol_table"]
                logger.info(f"client_id:{client_id} - 使用语种 {self.client_states[client_id]['current_language']} 初始化解码器")
            else:
                # 单语种模式或多语种模式下的默认配置
                decoder_args = self.args
                decoder_configs = self.configs
                decoder_symbol_table = self.symbol_table
                logger.info(f"client_id:{client_id} - 使用默认配置初始化解码器")

            self.client_states[client_id]["decoder"] = ASRDecoder(
                decoder_args, decoder_configs, decoder_symbol_table, custom_separator
            )
            self.required_decoding_window = self.client_states[client_id]["decoder"].decoding_window
            logger.info(f"client_id:{client_id} - 解码器初始化完成，分隔符: \"{self.client_states[client_id]['decoder'].blank}\"")


        # 2. 检查数据包索引, index 从0开始, 保证顺序到达
        index = json_data["index"]
        expected_index = self.client_states[client_id]['packet_index'] + 1
        if index != expected_index:
            await self.on_error(code=4002, message=f"Packet index error. Expected: {expected_index}, Got: {index}", client_id=client_id)
            return False
        # 更新索引状态
        self.client_states[client_id]['packet_index'] = index

        # 3. 是否为最后一个数据包
        is_final = json_data['is_final']
        if is_final:
            self.client_states[client_id]['is_final'] = True

        # 4. 检查数据包发送的实时率
        current_time = time.time()
        last_packet_time = self.client_states[client_id].get('last_packet_time', None)
        if last_packet_time is None:   # 第一个数据包
            self.client_states[client_id]['is_first'] = True
        else:
            self.client_states[client_id]['is_first'] = False
            if current_time - last_packet_time > self.packet_interval:
                current_time_fmt = format_time(current_time)
                last_time_fmt = format_time(last_packet_time)
                await self.on_error(
                    code=4003,
                    message=f"Audio packet interval timeout ({self.packet_interval} s). Last packet time: {last_time_fmt}, Current packet time: {current_time_fmt}",
                    client_id=client_id
                )
                return False
        # 更新数据包到达时间
        self.client_states[client_id]['last_packet_time'] = current_time  

        # 5. 检查采样率一致性
        sample_rate = json_data['sample_rate']
        last_sample_rate = self.client_states[client_id].get('sample_rate', None)
        
        if last_sample_rate is not None and sample_rate != last_sample_rate:
            await self.on_error(
                code=4004,
                message=f"Sample rate mismatch. Last: {last_sample_rate}, Current: {sample_rate}",
                client_id=client_id
            )
            return False

        if sample_rate not in self.valid_sample_rate_list:
            await self.on_error(
                code=4005,
                message=f"Invalid sample rate. Supported rates: {self.valid_sample_rate_list}",
                client_id=client_id
            )
            return False
    
        # 6.1 解码Base64编码的音频数据
        try:
            audio_data_base64 = json_data["audio_data"]
            pcm_bytes = base64.b64decode(audio_data_base64)
        except Exception as e:
            await self.on_error(code=4006, message=f"Audio data decoding error: {str(e)}. Value of \"audio_data\" shoud be encoded by base64, for example: packet = pcm_data[:{self.expected_data_size}], request_data[\"audio_data\"] = base64.b64encode(packet).decode('utf-8')", client_id=client_id)
            return False
        # 6.2 检查pcm音频数据流大小
        if is_final:
            if len(pcm_bytes) > self.expected_data_size:
                await self.on_error(
                    code=4007,
                    message=f"Final packet audio data size exceeds expected size (Expected: {self.expected_data_size}, Got: {len(pcm_bytes)})",
                    client_id=client_id
                )
                return False
        else:
            if len(pcm_bytes) != self.expected_data_size:
                await self.on_error(
                    code=4008,
                    message=f"Non-final packet audio data size mismatch (Expected: {self.expected_data_size}, Got: {len(pcm_bytes)}). Expected data size: based on 16000 sr, mono channel, 16-bit audio stream, 400 ms of audio data packet is sent each time",
                    client_id=client_id
                )
                return False

        # 6.3 提取特征并处理LID逻辑
        try:
            waveform = self.feat_pipe.to_waveform(pcm_bytes)  # torch.Tensor
            if is_final and waveform.size(0) < self.frame_samples * sample_rate:
                # 最后一个数据包达不到最小数据要求（小于一个帧长）则跳过
                return True

            # 6.3.1 静音检测和LID处理逻辑
            await self._process_silence_and_lid(client_id, waveform, sample_rate, is_final)

            # 6.3.2 只有在LID确认后或LID未启用时才提取ASR特征
            if (not self.client_states[client_id]["lid_enabled"] or
                self.client_states[client_id]["lid_confirmed"] or
                not self.lid_manager or
                not self.lid_manager.is_available()):

                feat = self.feat_pipe.feat_func(waveform, sample_rate)   # torch.Tensor
                self.client_states[client_id]['feat_buffer'].append(feat)
                # logger.debug(f"client_id:{client_id} - >>> waveform shape: {waveform.shape}")
                # logger.debug(f"client_id:{client_id} - >>> feat shape: {feat.shape}")

                # 6.4 更新帧数
                cur_frames = feat.shape[0]
                self.client_states[client_id]['frame_nums'] += cur_frames
                logger.info(f"client_id:{client_id} - >>> [解析] 第{index}个数据包, 累计帧数: {self.client_states[client_id]['frame_nums']}")
            else:
                logger.debug(f"client_id:{client_id} - >>> [等待LID] 第{index}个数据包, 等待语种识别确认")

            return True
        except Exception as e:
            await self.on_error(code=4009, message=f"Feature extraction error: {str(e)}", client_id=client_id)
            return False

    async def _process_silence_and_lid(self, client_id: str, waveform: torch.Tensor, sample_rate: int, is_final: bool):
        """
        处理静音检测和语种识别逻辑，支持多语种对话场景
        Args:
            client_id: 客户端ID
            waveform: 音频波形
            sample_rate: 采样率
            is_final: 是否为最后一个数据包
        """
        if not self.lid_manager or not self.lid_manager.is_available():
            return

        if not self.client_states[client_id]["lid_enabled"]:
            return

        # 检测当前帧是否包含语音
        current_has_speech = self.lid_manager.detect_speech(waveform, sample_rate)
        current_frame = self.client_states[client_id]['packet_index']

        if current_has_speech:
            # 检测到语音
            self.client_states[client_id]["silence_frames"] = 0

            # 如果这是静音后的第一个语音帧，重置LID状态
            if (self.client_states[client_id]["lid_confirmed"] and
                current_frame - self.client_states[client_id]["last_speech_frame"] > self.client_states[client_id]["silence_threshold"]):
                logger.info(f"client_id:{client_id} - 检测到静音后的新语音片段，重置LID状态")
                await self._reset_lid_state(client_id)

            self.client_states[client_id]["last_speech_frame"] = current_frame
        else:
            # 检测到静音
            self.client_states[client_id]["silence_frames"] += 1

        # 如果LID已确认且当前是静音，不需要进行LID处理
        if self.client_states[client_id]["lid_confirmed"] and not current_has_speech:
            return

        # 缓存音频数据（只在有语音或LID未确认时缓存）
        if current_has_speech or not self.client_states[client_id]["lid_confirmed"]:
            self.client_states[client_id]["audio_buffer"].append(waveform)
            audio_duration = waveform.shape[0] / sample_rate
            self.client_states[client_id]["audio_duration"] += audio_duration

        # 检查是否需要进行LID
        should_perform_lid = False

        # 第一次检测：0.4秒后检查是否有有效语音
        if (self.client_states[client_id]["lid_attempts"] == 0 and
            self.client_states[client_id]["audio_duration"] >= 0.4):

            # 拼接所有音频
            combined_audio = torch.cat(self.client_states[client_id]["audio_buffer"], dim=0)

            # VAD检测
            has_speech = self.lid_manager.detect_speech(combined_audio, sample_rate)
            self.client_states[client_id]["vad_speech_detected"] = has_speech

            if has_speech:
                should_perform_lid = True
                logger.info(f"client_id:{client_id} - 片段{self.client_states[client_id]['segment_count']} 检测到有效语音，开始语种识别")
            else:
                logger.debug(f"client_id:{client_id} - 未检测到有效语音，继续等待")

        # 后续检测：每0.4秒进行一次，最多6次（2.4秒）
        elif (self.client_states[client_id]["vad_speech_detected"] and
              self.client_states[client_id]["lid_attempts"] < self.client_states[client_id]["lid_max_attempts"] and
              (self.client_states[client_id]["audio_duration"] >=
               (self.client_states[client_id]["lid_attempts"] + 1) * 0.4)):
            should_perform_lid = True

        # 最后一个数据包时强制进行LID（如果还未确认）
        elif is_final and self.client_states[client_id]["vad_speech_detected"]:
            should_perform_lid = True

        if should_perform_lid:
            await self._perform_lid(client_id, sample_rate)

    async def _reset_lid_state(self, client_id: str):
        """
        重置LID状态，用于新的语音片段
        Args:
            client_id: 客户端ID
        """
        logger.info(f"client_id:{client_id} - 重置LID状态，开始新的语音片段识别")

        # 重置LID相关状态
        self.client_states[client_id]["detected_language"] = None
        self.client_states[client_id]["language_confidence"] = 0.0
        self.client_states[client_id]["audio_buffer"] = []
        self.client_states[client_id]["audio_duration"] = 0.0
        self.client_states[client_id]["lid_attempts"] = 0
        self.client_states[client_id]["lid_confirmed"] = False
        self.client_states[client_id]["vad_speech_detected"] = False
        self.client_states[client_id]["segment_count"] += 1

        # 重置解码器，因为可能需要切换到新的语种
        if self.client_states[client_id]["decoder"] is not None:
            logger.info(f"client_id:{client_id} - 重置解码器以准备语种切换")
            self.client_states[client_id]["decoder"] = None

    async def _perform_lid(self, client_id: str, sample_rate: int):
        """
        执行语种识别
        Args:
            client_id: 客户端ID
            sample_rate: 采样率
        """
        try:
            # 拼接所有音频
            combined_audio = torch.cat(self.client_states[client_id]["audio_buffer"], dim=0)

            # 执行LID
            lid_result = self.lid_manager.predict_language(combined_audio, sample_rate)

            self.client_states[client_id]["lid_attempts"] += 1
            current_language = lid_result.get("predict", "unknown")
            current_confidence = lid_result.get("scores", {}).get(current_language, 0.0)

            logger.info(f"client_id:{client_id} - LID尝试 {self.client_states[client_id]['lid_attempts']}: "
                       f"语种={current_language}, 置信度={current_confidence}")

            # 更新检测结果
            self.client_states[client_id]["detected_language"] = current_language
            self.client_states[client_id]["language_confidence"] = current_confidence

            # 判断是否确认结果
            should_confirm = (
                self.client_states[client_id]["lid_attempts"] >= self.client_states[client_id]["lid_max_attempts"] or
                current_confidence >= 0.8 or  # 高置信度
                self.client_states[client_id]["audio_duration"] >= 2.4  # 达到最大时长
            )

            if should_confirm:
                self.client_states[client_id]["lid_confirmed"] = True
                logger.info(f"client_id:{client_id} - LID结果确认: 语种={current_language}, 置信度={current_confidence}")

                # 根据识别的语种切换ASR模型（多语种模式）
                if self.multi_lang_asr_manager and current_language != "unknown":
                    success = await self._switch_asr_model(client_id, current_language)
                    if not success:
                        logger.warning(f"client_id:{client_id} - 切换到语种 {current_language} 失败，使用默认模型")
                        # 使用默认语种（中文）
                        await self._switch_asr_model(client_id, "zh")

        except Exception as e:
            logger.error(f"client_id:{client_id} - LID执行失败: {e}")
            # LID失败时，确认为未知语种，继续使用默认ASR
            self.client_states[client_id]["detected_language"] = "unknown"
            self.client_states[client_id]["lid_confirmed"] = True

    async def _switch_asr_model(self, client_id: str, target_language: str) -> bool:
        """
        切换到指定语种的ASR模型

        Args:
            client_id: 客户端ID
            target_language: 目标语种代码

        Returns:
            bool: 是否切换成功
        """
        try:
            if not self.multi_lang_asr_manager:
                logger.warning(f"client_id:{client_id} - 多语种管理器未初始化，无法切换模型")
                return False

            # 切换全局ONNX模型会话
            if not self.multi_lang_asr_manager.switch_to_language(target_language):
                logger.error(f"client_id:{client_id} - 切换到语种 {target_language} 失败")
                return False

            # 获取目标语种的配置和词表
            target_args = self.multi_lang_asr_manager.get_args(target_language)
            target_symbol_table = self.multi_lang_asr_manager.get_symbol_table(target_language)
            target_config = self.multi_lang_asr_manager.get_config(target_language)

            if not target_args or not target_symbol_table or not target_config:
                logger.error(f"client_id:{client_id} - 获取语种 {target_language} 的配置失败")
                return False

            # 更新客户端状态中的语种信息
            self.client_states[client_id]["current_language"] = target_language
            self.client_states[client_id]["current_symbol_table"] = target_symbol_table
            self.client_states[client_id]["current_config"] = target_config
            self.client_states[client_id]["current_args"] = target_args

            # 重置解码器以使用新的语种配置
            if self.client_states[client_id]["decoder"] is not None:
                logger.info(f"client_id:{client_id} - 重置解码器以使用新语种配置")
                self.client_states[client_id]["decoder"] = None

            logger.info(f"client_id:{client_id} - 成功切换到语种: {target_language}")
            return True

        except Exception as e:
            logger.error(f"client_id:{client_id} - 切换ASR模型到 {target_language} 时发生异常: {e}")
            return False

    async def on_decode(self, client_id: str) -> Tuple[bool, Optional[str]]:
        """
        当帧数满足解码所需的最小帧数时, 开始逐 chunk 解码。
        Args:
            client_id (str): 客户端唯一标识
        Returns:
            Tuple[bool, Optional[str]]: 是否成功解码(True/False), 解码结果或空字符串
        """
        try:
            # 检查是否需要等待LID确认
            if (self.client_states[client_id]["lid_enabled"] and
                self.lid_manager and
                self.lid_manager.is_available() and
                not self.client_states[client_id]["lid_confirmed"]):

                # 如果是最后一个数据包但LID还未确认，强制确认
                if self.client_states[client_id]['is_final']:
                    self.client_states[client_id]["lid_confirmed"] = True
                    if not self.client_states[client_id]["detected_language"]:
                        self.client_states[client_id]["detected_language"] = "unknown"
                    logger.info(f"client_id:{client_id} - 最后数据包，强制确认LID结果")
                else:
                    # 等待LID确认
                    logger.debug(f"client_id:{client_id} - 等待LID确认，暂停解码")
                    await asyncio.sleep(0.01)
                    return (True, "")

            # 确保解码器已初始化
            if self.client_states[client_id]["decoder"] is None:
                custom_separator = self.client_states[client_id]["custom_separator"]

                # 多语种模式下使用动态切换的配置
                if self.multi_lang_asr_manager and self.client_states[client_id]["current_args"]:
                    # 使用切换后的语种配置
                    decoder_args = self.client_states[client_id]["current_args"]
                    decoder_configs = self.client_states[client_id]["current_config"]
                    decoder_symbol_table = self.client_states[client_id]["current_symbol_table"]
                    logger.info(f"client_id:{client_id} - 延迟初始化解码器，使用语种: {self.client_states[client_id]['current_language']}")
                else:
                    # 单语种模式或多语种模式下的默认配置
                    decoder_args = self.args
                    decoder_configs = self.configs
                    decoder_symbol_table = self.symbol_table
                    logger.info(f"client_id:{client_id} - 延迟初始化解码器，使用默认配置")

                self.client_states[client_id]["decoder"] = ASRDecoder(
                    decoder_args, decoder_configs, decoder_symbol_table, custom_separator
                )
                self.required_decoding_window = self.client_states[client_id]["decoder"].decoding_window
                logger.info(f"client_id:{client_id} - 延迟初始化解码器完成")

            while True:
                frame_nums = self.client_states[client_id]['frame_nums']

                if frame_nums >= self.required_decoding_window:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 开始解码")

                    # 拼接特征并添加 batch 维度
                    accum_feats = torch.cat(self.client_states[client_id]['feat_buffer'], dim=0).unsqueeze(0)

                    is_final = self.client_states[client_id]['is_final']
                    asr_model = self.client_states[client_id]['decoder']
                    asr_model.decode(accum_feats, client_id, is_final) # 累积的解码结果存储在 asr_model.result 中
                    last_result = self.client_states[client_id]['decoder_result']
                    cur_result = asr_model.result
                    if is_final:
                        logger.info(f"client_id:{client_id} - *** 最后一个数据包完成解码 ***")
                        self.client_states[client_id]['is_final_result'] = True
                        return (True, cur_result)    # 最后一个数据包，无论识别结果变不变，都要发送响应数据包

                    if cur_result != last_result:
                        logger.debug(f"client_id:{client_id} - chunk 完成解码, 更新识别结果")
                        self.client_states[client_id]['decoder_result'] = cur_result
                        return (True, cur_result)
                    else:
                        logger.debug(f"client_id:{client_id} - chunk 完成解码, 识别结果不变")
                        return (True, "")
                else:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 继续等待数据包")
                    await asyncio.sleep(0.01)   # 低频轮询避免忙等待 (10ms 间隔)
                    return (True, "")

        except Exception as e:
            await self.on_error(code=5001, message=f"Decode error", client_id=client_id)
            return (False, None)
