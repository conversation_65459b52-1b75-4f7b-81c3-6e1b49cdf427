#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  infer_onnx_lid.py
# Time    :  2024/12/18 10:18:36
# Author  :  lh
# 加载 lid onnx 模型进行推理

import argparse
import onnxruntime as ort
import torch
import torchaudio
import torchaudio.compliance.kaldi as kaldi
from vad_utils import SpeechVadFrontend
import numpy as np
import yaml, json, math, os
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback
from typing import List


VALID_EXT = ['.wav', '.mp3', '.flac', '.opus']

def get_args():
    parser = argparse.ArgumentParser(description=f'Detect language for audio, support extention: {VALID_EXT}')
    parser.add_argument('--input', required=True, help='Single audio file or multiple audio list (eg. /ws/test_data/test.list)')
    parser.add_argument('--onnx_path', default="/ws/res/lid.onnx", help='Path of onnx model')
    parser.add_argument('--config', default="/ws/res/config.yaml", help='Path of config.yaml')
    parser.add_argument('--spk2id', default="/ws/res/spk2id.json", help="Path of spk2id.json")
    parser.add_argument('--do_cmvn', required=False, default=True)
    parser.add_argument('--verbose','-v',action='store_true', help='Enable verbose mode for more detailed print information')
    parser.add_argument('--num_threads', type=int, default=8)
    args = parser.parse_args()
    return args


def compute_fbank(waveform,
                  sample_rate,
                  num_mel_bins=80,
                  frame_length=25,
                  frame_shift=10,
                  dither=0.0):
    """ Extract fbank, simlilar to the one in wespeaker.dataset.processor,
        While integrating the wave reading and CMN.
    """
    waveform = waveform * (1 << 15)
    mat = kaldi.fbank(waveform,
                      num_mel_bins=num_mel_bins,
                      frame_length=frame_length,
                      frame_shift=frame_shift,
                      dither=dither,
                      sample_frequency=sample_rate,
                      window_type='hamming',
                      use_energy=False)
    return mat


def _load_json_cmvn(json_cmvn_file):
    """ Load the json format cmvn stats file and calculate cmvn

    Args:
        json_cmvn_file: cmvn stats file in json format

    Returns:
        a numpy array of [means, vars]
    """
    with open(json_cmvn_file) as f:
        cmvn_stats = json.load(f)

    means = cmvn_stats['mean_stat']
    variance = cmvn_stats['var_stat']
    count = cmvn_stats['frame_num']
    for i in range(len(means)):
        means[i] /= count
        variance[i] = variance[i] / count - means[i] * means[i]
        if variance[i] < 1.0e-20:
            variance[i] = 1.0e-20
        variance[i] = 1.0 / math.sqrt(variance[i])
    cmvn = np.array([means, variance])
    return cmvn


def load_cmvn(cmvn_file):
    cmvn_file_export = cmvn_file + '.export'
    if os.path.exists(cmvn_file_export):
        with open(cmvn_file_export) as f:
            cmvn_stats = json.load(f)
            cmvn_stats = np.array([cmvn_stats['mean_stat'], cmvn_stats['var_stat']])
    else:
        cmvn_stats =  _load_json_cmvn(cmvn_file)

    return cmvn_stats


def apply_cvn(mat):
    # CMN, without CVN
    mat = mat - torch.mean(mat, dim=0)
    return mat


def apply_global_cmvn(mat, cmvn_file):
    if cmvn_file is not None:
        mean, istd = load_cmvn(cmvn_file)
        mean, var = torch.from_numpy(mean).float(), torch.from_numpy(istd).float()
        if mean is not None:
            assert var is not None
            mat = mat - mean
            mat = mat * var
    return mat


def load_wav(wav_path, resample_rate):
    waveform, sample_rate = torchaudio.load(wav_path)
    if sample_rate != resample_rate:
        waveform = torchaudio.transforms.Resample(
            orig_freq=sample_rate, new_freq=resample_rate)(waveform)
    return waveform

vad_conf = {
        'vad_type': 'webrtcvad',
        'vad_level': 1,
        'frame_length': 30,
        'window_size' : 10,
        'seg_thres' : 0.9, 
        'max_speech_len' : 10,
        'min_speech_len' : 5,
        'merge_sil_thres' : 2
}
vad_frontend = SpeechVadFrontend(**vad_conf)

def audio_seg(waveform, sr=8000):
    try:
        segments, segment_lens, segment_ranges = vad_frontend.get_all_speech_segments(waveform, sr)
        index = segment_lens.index(max(segment_lens))
        return segments[index], segment_lens[index]/1000.0
    except:
        return waveform[0], waveform.shape[-1]/1000.0

def single_wav_exec(wav_path, 
                    session, 
                    input_names, 
                    output_names,  
                    configs,  
                    do_cmvn=True):
    resample_rate = configs['dataset_args']['resample_rate']
    waveform = load_wav(wav_path.strip(), resample_rate)
    waveform, duration = audio_seg(waveform, sr=resample_rate)
    waveform = waveform.unsqueeze(0)
    feats = compute_fbank(waveform, resample_rate, **configs['dataset_args']['fbank_args'])
    if do_cmvn:
        feats = apply_global_cmvn(feats, configs['dataset_args']['cmvn_file'])
    else:
        feats = apply_cvn(feats)

    feats = feats.unsqueeze(0).numpy() # add batch dimension
    if len(input_names) == 1:
        ort_inputs = {input_names[0]: feats}
    elif len(input_names) == 2:
        fake_targets = torch.randint(12, (1, )).numpy()
        ort_inputs = {input_names[0]: feats, input_names[1]: fake_targets}
    else:
        exit(1)

    ort_outs = session.run(output_names, ort_inputs) 
    return ort_outs[0], duration


def make_result(output, wavname, duration_for_lid, label, spk2id_dict):
    spks = list(spk2id_dict.keys())

    index = np.argmax(output)
    predict_id = index.item()

    # make score_dict
    score_dict = {}
    mm = max(output[0]).item()
    mi = min(output[0]).item()
    for k, i in spk2id_dict.items():
        s = (output[0][i].item()-mi)/(mm-mi)
        score_dict[k] = round(s, 2)
    res = {
        "wav": wavname,
        "duration": duration_for_lid, 
        "target": label,
        "predict": spks[predict_id],
        "scores": score_dict
    }
    return res


def predict_audio(input: dict, 
                session: ort.InferenceSession, 
                input_names: List, 
                output_names: List,  
                configs: dict,  
                spk2id_dict: dict,
                do_cmvn=True):
    dic = input
    output, segment_duration = single_wav_exec(dic['wav'], session, input_names, output_names, configs, do_cmvn=do_cmvn)
    result = make_result(output, dic['wav'], segment_duration, dic['spk'], spk2id_dict)
    return result

def load_model(args):
    so = ort.SessionOptions()
    so.inter_op_num_threads = 1
    so.intra_op_num_threads = 1
    session = ort.InferenceSession(args.onnx_path, sess_options=so)
    input_names = [input.name for input in session.get_inputs()]
    output_names = [output.name for output in session.get_outputs()]
    print(f"Load model done.")
    return session, input_names, output_names

def load_dict(args):
    spk2id_dict = {}
    with open(args.spk2id, 'r') as fspk2id:
        spk2id_dict = json.loads(fspk2id.read())
    print(f"Load dict done.")
    return spk2id_dict

def main():
    # 处理参数
    args = get_args()
    
    # 加载配置、模型、字典
    print(f"---------------")
    with open(args.config, 'r') as fin:
        configs = yaml.load(fin, Loader=yaml.FullLoader)
    session, input_names, output_names = load_model(args)
    spk2id_dict = load_dict(args)
    params = (session, input_names, output_names, configs, spk2id_dict, args.do_cmvn)
    print(f"---------------")
    
    # 处理输入
    wav_path = args.input
    ext = os.path.splitext(wav_path)[1]

    # 单个输入
    if ext in VALID_EXT:
        inp = {'wav': wav_path, 'spk': ''}
        result = predict_audio(
            inp,
            *params
        )
        print(result)
    # 多个输入
    elif ext in ['.txt', '.list']:
        assert os.path.exists(wav_path), f"Error: File {wav_path} does not exists."
        data = []
        with open(wav_path, 'r', encoding='utf-8')as fr:
            for line in fr.readlines():
                dic = json.loads(line.strip())
                data.append(dic)
        # 多线程执行
        workers = min(len(data), args.num_threads)
        with ThreadPoolExecutor(max_workers=workers) as executor:
            futures = [
                executor.submit(predict_audio, 
                                dic, 
                                *params
                                )
                for dic in data
            ]
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if args.verbose:
                        result = json.dumps(result, indent=4, ensure_ascii=False)
                        print(result)
                    else:
                        print(f"Detect language: {result['predict']} | {result['wav']}")

                except Exception as e:
                    print(f"Error processing line: {e}")
                    traceback.print_exc()
                    exit(1)
    else:
        print(f"Valid extention for single audio: {VALID_EXT}, multiple audio please refer to: /ws/test_data/test.list")
        exit(1)


if __name__ == '__main__':
    main()
